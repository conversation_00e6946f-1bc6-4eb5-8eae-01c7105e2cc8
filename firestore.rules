rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Règles pour les sessions - seul le propriétaire peut accéder à ses sessions
    match /sessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Règles pour les archives - seul le propriétaire peut accéder à ses archives
    match /archives/{archiveId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Règles pour les agences - seul le propriétaire peut accéder à ses agences
    match /agencies/{agencyId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Interdire tout autre accès
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
