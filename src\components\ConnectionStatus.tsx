// 🔧 CISCO: Indicateur de statut de connexion
// Affiche l'état Firebase et mode offline

import React from 'react';
import { ConnectionStatus as ConnectionStatusType } from '../utils/firebaseErrorHandler';

interface ConnectionStatusProps {
  status: ConnectionStatusType;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  status, 
  className = '' 
}) => {
  const getStatusInfo = () => {
    if (!status.isOnline) {
      return {
        icon: '🔴',
        text: 'Hors ligne',
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200'
      };
    }

    if (!status.isFirebaseConnected) {
      return {
        icon: '🟡',
        text: 'Mode cache local',
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        borderColor: 'border-yellow-200'
      };
    }

    if (status.retryCount > 0) {
      return {
        icon: '🟠',
        text: `Reconnexion... (${status.retryCount})`,
        bgColor: 'bg-orange-100',
        textColor: 'text-orange-800',
        borderColor: 'border-orange-200'
      };
    }

    return {
      icon: '🟢',
      text: 'Connecté',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`
      flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium
      ${statusInfo.bgColor} ${statusInfo.textColor} ${statusInfo.borderColor}
      ${className}
    `}>
      <span className="text-xs">{statusInfo.icon}</span>
      <span>{statusInfo.text}</span>
      
      {status.lastError && (
        <div className="ml-2 text-xs opacity-75" title={status.lastError}>
          ⚠️
        </div>
      )}
    </div>
  );
};

// 🔧 Version compacte pour la barre de navigation
export const ConnectionStatusCompact: React.FC<ConnectionStatusProps> = ({ 
  status, 
  className = '' 
}) => {
  const getStatusInfo = () => {
    if (!status.isOnline) {
      return { icon: '🔴', title: 'Hors ligne - Données en cache local uniquement' };
    }

    if (!status.isFirebaseConnected) {
      return { 
        icon: '🟡', 
        title: 'Mode cache local - Firebase déconnecté' + 
               (status.lastError ? `\nErreur: ${status.lastError}` : '')
      };
    }

    if (status.retryCount > 0) {
      return { 
        icon: '🟠', 
        title: `Reconnexion en cours... (tentative ${status.retryCount})` 
      };
    }

    return { icon: '🟢', title: 'Connecté à Firebase - Synchronisation active' };
  };

  const statusInfo = getStatusInfo();

  return (
    <div 
      className={`cursor-help ${className}`}
      title={statusInfo.title}
    >
      <span className="text-lg">{statusInfo.icon}</span>
    </div>
  );
};

// 🔧 Bannière d'alerte pour les problèmes de connexion
export const ConnectionAlert: React.FC<{ status: ConnectionStatusType }> = ({ status }) => {
  if (status.isOnline && status.isFirebaseConnected && status.retryCount === 0) {
    return null; // Tout va bien, pas d'alerte
  }

  const getMessage = () => {
    if (!status.isOnline) {
      return {
        type: 'error' as const,
        title: 'Connexion Internet perdue',
        message: 'Vos données sont sauvegardées localement et seront synchronisées dès le retour de la connexion.'
      };
    }

    if (!status.isFirebaseConnected) {
      return {
        type: 'warning' as const,
        title: 'Problème de synchronisation',
        message: 'Impossible de se connecter au serveur. Vos données sont sauvegardées localement.'
      };
    }

    if (status.retryCount > 0) {
      return {
        type: 'info' as const,
        title: 'Reconnexion en cours',
        message: `Tentative de reconnexion ${status.retryCount}...`
      };
    }

    return null;
  };

  const alertInfo = getMessage();
  if (!alertInfo) return null;

  const getAlertStyles = () => {
    switch (alertInfo.type) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className={`
      border-l-4 p-4 mb-4 rounded-r-lg
      ${getAlertStyles()}
    `}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {alertInfo.type === 'error' && <span className="text-red-400">⚠️</span>}
          {alertInfo.type === 'warning' && <span className="text-yellow-400">⚠️</span>}
          {alertInfo.type === 'info' && <span className="text-blue-400">ℹ️</span>}
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium">{alertInfo.title}</h3>
          <p className="mt-1 text-sm">{alertInfo.message}</p>
          {status.lastError && (
            <p className="mt-2 text-xs opacity-75">
              Détail technique: {status.lastError}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
