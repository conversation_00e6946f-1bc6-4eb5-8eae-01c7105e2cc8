# 🚀 Guide de Déploiement Netlify

## Variables d'Environnement Requises

Configurez ces variables dans Netlify (Site Settings > Environment Variables) :

- `VITE_FIREBASE_API_KEY`
- `VITE_FIREBASE_AUTH_DOMAIN` 
- `VITE_FIREBASE_PROJECT_ID`
- `VITE_FIREBASE_STORAGE_BUCKET`
- `VITE_FIREBASE_MESSAGING_SENDER_ID`
- `VITE_FIREBASE_APP_ID`
- `VITE_FIREBASE_MEASUREMENT_ID`

## Commandes de Déploiement

```bash
# Vérification complète avant déploiement
npm run deploy:ready

# Vérification rapide des fichiers
npm run pre-deploy
```

## Configuration Netlify

- **Build command:** `npm run build`
- **Publish directory:** `dist`
- **Node version:** `18`
