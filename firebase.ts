// src/firebase.ts

import { initializeApp, FirebaseApp } from "firebase/app";
import { getAuth, Auth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getAnalytics, Analytics } from "firebase/analytics";
import { firebaseConfig } from "./firebase-config";

// --- Configuration Firebase ---
// Utilisation de la configuration importée depuis firebase-config.ts
// Cette approche fonctionne à la fois en développement et en production

// --- Initialisation des services Firebase ---
// On initialise l'application et on exporte les services dont on aura besoin.
const app: FirebaseApp = initializeApp(firebaseConfig);
const auth: Auth = getAuth(app);

// 🔧 CISCO: Configuration Firestore corrigée pour Netlify
// Utiliser la base de données par défaut si VITE_FIRESTORE_DB_ID n'est pas défini
const firestoreDbId = ((import.meta as any).env as any).VITE_FIRESTORE_DB_ID;
const db: Firestore = firestoreDbId ?
  getFirestore(app, firestoreDbId) :
  getFirestore(app); // Base de données par défaut

const analytics: Analytics = getAnalytics(app);
const googleProvider = new GoogleAuthProvider();

// 🔧 CISCO: Debug pour diagnostiquer les problèmes Netlify
if (typeof window !== 'undefined') {
  console.log('🔧 Firebase Configuration Debug:', {
    projectId: firebaseConfig.projectId,
    authDomain: firebaseConfig.authDomain,
    firestoreDbId: firestoreDbId || '(default)',
    currentDomain: window.location.hostname,
    environment: ((import.meta as any).env as any).MODE,
    hasAllRequiredVars: Object.values(firebaseConfig).every(v => v !== undefined)
  });
}

export { app, auth, db, analytics, googleProvider };


