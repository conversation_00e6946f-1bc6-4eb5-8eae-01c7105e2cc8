// 🔧 CISCO: Hook hybride Firebase + Cache local
// Résout le problème de session/historique sur Netlify

import { useState, useCallback, useEffect } from 'react';
import { Session, Agency } from '../types';
import { indexedDBCache } from '../utils/indexedDBCache';
import { firebaseErrorHandler, ConnectionStatus } from '../utils/firebaseErrorHandler';
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  orderBy,
  deleteDoc,
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore';
import { db, auth } from '../firebase';

export const useHybridFirestore = (userId: string | undefined) => {
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [history, setHistory] = useState<Session[]>([]);
  const [archives, setArchives] = useState<Session[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(
    firebaseErrorHandler.getConnectionStatus()
  );
  const [isLoading, setIsLoading] = useState(true);

  // 🔧 Écouter les changements de statut de connexion
  useEffect(() => {
    const handleStatusChange = (status: ConnectionStatus) => {
      setConnectionStatus(status);
    };

    firebaseErrorHandler.addStatusListener(handleStatusChange);
    return () => firebaseErrorHandler.removeStatusListener(handleStatusChange);
  }, []);

  // 🔧 Charger les données depuis le cache local
  const loadFromCache = useCallback(async () => {
    if (!userId) return;

    try {
      const cachedData = await indexedDBCache.getFromCache(userId);
      if (cachedData) {
        console.log('📦 Chargement depuis le cache local');
        setAgencies(cachedData.agencies);
        setHistory(cachedData.sessions);
        setArchives(cachedData.archives);
        return true;
      }
    } catch (error) {
      console.error('❌ Erreur chargement cache:', error);
    }
    return false;
  }, [userId]);

  // 🔧 Sauvegarder dans le cache local
  const saveToCache = useCallback(async () => {
    if (!userId) return;

    await indexedDBCache.saveToCache(userId, {
      agencies,
      sessions: history,
      archives
    });
  }, [userId, agencies, history, archives]);

  // 🔧 Récupérer les agences (Firebase + Cache)
  const fetchAgencies = useCallback(async () => {
    if (!userId) return;

    // Vérifier l'authentification Firebase
    if (!auth.currentUser) {
      await loadFromCache();
      return;
    }

    try {
      const result = await firebaseErrorHandler.retryWithBackoff(async () => {
        firebaseErrorHandler.logRequestDetails('fetchAgencies');
        const userDocRef = doc(db, "users", userId);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
          return userDoc.data().agencies || [];
        }
        return [];
      }, 'fetchAgencies');

      setAgencies(result);
      // Sauvegarder dans le cache
      await indexedDBCache.saveToCache(userId, { agencies: result });
      
    } catch (error) {
      console.warn('⚠️ Échec récupération agences Firebase, utilisation du cache');
      await loadFromCache();
    }
  }, [userId, loadFromCache]);

  // 🔧 Récupérer l'historique (Firebase + Cache)
  const fetchHistory = useCallback(async () => {
    if (!userId) return;

    if (!auth.currentUser) {
      await loadFromCache();
      return;
    }

    try {
      const result = await firebaseErrorHandler.retryWithBackoff(async () => {
        firebaseErrorHandler.logRequestDetails('fetchHistory');
        const sessionsColRef = collection(db, 'sessions');
        const q = query(
          sessionsColRef, 
          where("userId", "==", userId), 
          orderBy("startTime", "desc")
        );
        
        const querySnapshot = await getDocs(q);
        const sessions: Session[] = [];
        querySnapshot.forEach((doc) => {
          sessions.push({ id: doc.id, ...doc.data() } as Session);
        });
        
        return sessions;
      }, 'fetchHistory');

      setHistory(result);
      // Sauvegarder dans le cache
      await indexedDBCache.saveToCache(userId, { sessions: result });
      
    } catch (error) {
      console.warn('⚠️ Échec récupération historique Firebase, utilisation du cache');
      await loadFromCache();
    }
  }, [userId, loadFromCache]);

  // 🔧 Récupérer les archives (Firebase + Cache)
  const fetchArchives = useCallback(async () => {
    if (!userId) return;

    if (!auth.currentUser) {
      await loadFromCache();
      return;
    }

    try {
      const result = await firebaseErrorHandler.retryWithBackoff(async () => {
        firebaseErrorHandler.logRequestDetails('fetchArchives');
        const archivesColRef = collection(db, 'archives');
        const q = query(
          archivesColRef, 
          where("userId", "==", userId), 
          orderBy("startTime", "desc")
        );
        
        const querySnapshot = await getDocs(q);
        const archivesList: Session[] = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Session));
        
        return archivesList;
      }, 'fetchArchives');

      setArchives(result);
      // Sauvegarder dans le cache
      await indexedDBCache.saveToCache(userId, { archives: result });
      
    } catch (error) {
      console.warn('⚠️ Échec récupération archives Firebase, utilisation du cache');
      await loadFromCache();
    }
  }, [userId, loadFromCache]);

  // 🔧 Sauvegarder une session (Firebase + Cache)
  const saveSession = useCallback(async (session: Omit<Session, 'id'>) => {
    if (!userId) return;

    // Valider les données avant envoi
    if (!firebaseErrorHandler.validateFirebaseRequest(session, ['userId', 'startTime', 'agencyId'])) {
      console.error('❌ Session invalide, sauvegarde annulée');
      return;
    }

    // Créer une session temporaire avec ID local pour le cache
    const tempSession: Session = {
      ...session,
      id: `temp-${Date.now()}-${Math.random()}`
    };

    // Ajouter immédiatement au cache local et à l'état
    setHistory(prev => [tempSession, ...prev]);
    await indexedDBCache.addSessionToCache(userId, tempSession);

    // Essayer de sauvegarder sur Firebase
    if (connectionStatus.isFirebaseConnected && auth.currentUser) {
      try {
        const result = await firebaseErrorHandler.retryWithBackoff(async () => {
          firebaseErrorHandler.logRequestDetails('saveSession', session);
          const sessionsColRef = collection(db, 'sessions');
          return await addDoc(sessionsColRef, session);
        }, 'saveSession');

        // Remplacer la session temporaire par la vraie avec l'ID Firebase
        const realSession: Session = { ...session, id: result.id };
        setHistory(prev => prev.map(s => s.id === tempSession.id ? realSession : s));
        await indexedDBCache.removeSessionFromCache(userId, tempSession.id);
        await indexedDBCache.addSessionToCache(userId, realSession);

        console.log('✅ Session sauvegardée sur Firebase et cache');
        
      } catch (error) {
        console.warn('⚠️ Échec sauvegarde Firebase, session conservée en cache local');
      }
    } else {
      console.log('📦 Session sauvegardée uniquement en cache local (mode offline)');
    }
  }, [userId, connectionStatus.isFirebaseConnected]);

  // 🔧 Initialisation des données
  useEffect(() => {
    if (userId) {
      setIsLoading(true);
      
      const initializeData = async () => {
        // Charger d'abord depuis le cache pour une réponse rapide
        const cacheLoaded = await loadFromCache();
        
        if (cacheLoaded) {
          setIsLoading(false);
        }

        // Puis essayer de synchroniser avec Firebase
        if (connectionStatus.isFirebaseConnected && auth.currentUser) {
          try {
            await Promise.all([
              fetchAgencies(),
              fetchHistory(),
              fetchArchives()
            ]);
          } catch (error) {
            console.warn('⚠️ Synchronisation Firebase échouée, utilisation du cache');
          }
        }
        
        setIsLoading(false);
      };

      // Délai pour laisser l'authentification se stabiliser
      const timer = setTimeout(initializeData, 1000);
      return () => clearTimeout(timer);
    } else {
      setAgencies([]);
      setHistory([]);
      setArchives([]);
      setIsLoading(false);
    }
  }, [userId, connectionStatus.isFirebaseConnected, loadFromCache, fetchAgencies, fetchHistory, fetchArchives]);

  // 🔧 Sauvegarder automatiquement dans le cache quand les données changent
  useEffect(() => {
    if (userId && !isLoading) {
      saveToCache();
    }
  }, [agencies, history, archives, userId, isLoading, saveToCache]);

  // 🔧 Ajouter une agence (Firebase + Cache)
  const addAgency = useCallback(async (agencyName: string): Promise<Agency | null> => {
    if (!userId) return null;

    const newAgency: Agency = { id: `custom-${Date.now()}`, name: agencyName };

    // Ajouter immédiatement à l'état local et au cache
    setAgencies(prev => [...prev, newAgency]);
    await indexedDBCache.saveToCache(userId, { agencies: [...agencies, newAgency] });

    // Essayer de sauvegarder sur Firebase
    if (connectionStatus.isFirebaseConnected && auth.currentUser) {
      try {
        await firebaseErrorHandler.retryWithBackoff(async () => {
          const userDocRef = doc(db, "users", userId);
          await updateDoc(userDocRef, {
            agencies: arrayUnion(newAgency)
          });
        }, 'addAgency');

        console.log('✅ Agence ajoutée sur Firebase et cache');
      } catch (error) {
        console.warn('⚠️ Échec ajout agence Firebase, conservée en cache local');
      }
    }

    return newAgency;
  }, [userId, agencies, connectionStatus.isFirebaseConnected]);

  // 🔧 Supprimer une agence (Firebase + Cache)
  const deleteAgency = useCallback(async (agencyToDelete: Agency): Promise<boolean> => {
    if (!userId) return false;

    // Supprimer immédiatement de l'état local et du cache
    const updatedAgencies = agencies.filter(agency => agency.id !== agencyToDelete.id);
    setAgencies(updatedAgencies);
    await indexedDBCache.saveToCache(userId, { agencies: updatedAgencies });

    // Essayer de supprimer sur Firebase
    if (connectionStatus.isFirebaseConnected && auth.currentUser) {
      try {
        await firebaseErrorHandler.retryWithBackoff(async () => {
          const userDocRef = doc(db, "users", userId);
          await updateDoc(userDocRef, {
            agencies: arrayRemove(agencyToDelete)
          });
        }, 'deleteAgency');

        console.log('✅ Agence supprimée sur Firebase et cache');
      } catch (error) {
        console.warn('⚠️ Échec suppression agence Firebase, supprimée du cache local');
      }
    }

    return true;
  }, [userId, agencies, connectionStatus.isFirebaseConnected]);

  // 🔧 Vider l'historique (Firebase + Cache)
  const clearHistory = useCallback(async () => {
    if (!userId) return;

    // Vider immédiatement l'état local et le cache
    setHistory([]);
    await indexedDBCache.saveToCache(userId, { sessions: [] });

    // Essayer de vider sur Firebase
    if (connectionStatus.isFirebaseConnected && auth.currentUser) {
      try {
        await firebaseErrorHandler.retryWithBackoff(async () => {
          const sessionsColRef = collection(db, 'sessions');
          const q = query(sessionsColRef, where("userId", "==", userId));
          const querySnapshot = await getDocs(q);

          const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
          await Promise.all(deletePromises);
        }, 'clearHistory');

        console.log('✅ Historique vidé sur Firebase et cache');
      } catch (error) {
        console.warn('⚠️ Échec vidage historique Firebase, vidé du cache local');
      }
    }
  }, [userId, connectionStatus.isFirebaseConnected]);

  // 🔧 Méthodes d'export (fonctionnent avec le cache local)
  const getOldSessions = useCallback(() => {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    return history.filter(session => session.startTime < thirtyDaysAgo);
  }, [history]);

  const exportToJSON = useCallback((sessions: Session[], filename: string) => {
    const dataStr = JSON.stringify(sessions, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }, []);

  const exportToCSV = useCallback((sessions: Session[], filename: string) => {
    const headers = ['Date', 'Heure début', 'Heure fin', 'Durée', 'Agence', 'Activité'];
    const csvContent = [
      headers.join(','),
      ...sessions.map(session => [
        new Date(session.startTime).toLocaleDateString(),
        new Date(session.startTime).toLocaleTimeString(),
        session.endTime ? new Date(session.endTime).toLocaleTimeString() : 'En cours',
        session.endTime ? `${Math.round((session.endTime - session.startTime) / 60000)} min` : 'En cours',
        session.agencyName || 'N/A',
        session.activity || 'N/A'
      ].join(','))
    ].join('\n');

    const dataBlob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }, []);

  const exportToTXT = useCallback((sessions: Session[], filename: string) => {
    const txtContent = sessions.map(session =>
      `${new Date(session.startTime).toLocaleString()} - ${session.agencyName} - ${session.activity || 'N/A'}`
    ).join('\n');

    const dataBlob = new Blob([txtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  }, []);

  const exportToPDF = useCallback((sessions: Session[], filename: string) => {
    // Implémentation simplifiée - dans un vrai projet, utiliser jsPDF
    console.warn('Export PDF non implémenté dans cette version');
    exportToTXT(sessions, filename.replace('.pdf', '.txt'));
  }, [exportToTXT]);

  // 🔧 Archiver des sessions (Firebase + Cache)
  const archiveSessions = useCallback(async (sessionsToArchive: Session[]): Promise<boolean> => {
    if (!userId) return false;

    try {
      // Mettre à jour immédiatement le cache local
      await indexedDBCache.archiveSessionsInCache(userId, sessionsToArchive);

      // Mettre à jour l'état local
      const archivedIds = sessionsToArchive.map(s => s.id);
      setHistory(prev => prev.filter(s => !archivedIds.includes(s.id)));
      setArchives(prev => [...prev, ...sessionsToArchive]);

      // Essayer de synchroniser avec Firebase
      if (connectionStatus.isFirebaseConnected && auth.currentUser) {
        try {
          await firebaseErrorHandler.retryWithBackoff(async () => {
            // Ajouter aux archives Firebase
            const archivesColRef = collection(db, 'archives');
            const archivePromises = sessionsToArchive.map(session => {
              const { id, ...sessionData } = session;
              return addDoc(archivesColRef, sessionData);
            });
            await Promise.all(archivePromises);

            // Supprimer des sessions Firebase
            const deletePromises: Promise<any>[] = [];
            for (const session of sessionsToArchive) {
              const sessionsColRef = collection(db, 'sessions');
              const q = query(sessionsColRef,
                where('userId', '==', userId),
                where('startTime', '==', session.startTime)
              );
              const snap = await getDocs(q);
              snap.forEach(d => deletePromises.push(deleteDoc(d.ref)));
            }
            await Promise.all(deletePromises);
          }, 'archiveSessions');

          console.log('✅ Sessions archivées sur Firebase et cache');
        } catch (error) {
          console.warn('⚠️ Échec archivage Firebase, archivées en cache local');
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Erreur archivage sessions:', error);
      return false;
    }
  }, [userId, connectionStatus.isFirebaseConnected]);

  // 🔧 Supprimer une session de l'historique (Firebase + Cache)
  const deleteHistorySession = useCallback(async (sessionToDelete: Session): Promise<boolean> => {
    if (!userId) return false;

    try {
      // Supprimer immédiatement du cache local et de l'état
      setHistory(prev => prev.filter(s => s.id !== sessionToDelete.id));
      await indexedDBCache.removeSessionFromCache(userId, sessionToDelete.id);

      // Essayer de supprimer sur Firebase
      if (connectionStatus.isFirebaseConnected && auth.currentUser) {
        try {
          await firebaseErrorHandler.retryWithBackoff(async () => {
            const sessionsColRef = collection(db, 'sessions');
            const q = query(sessionsColRef,
              where('userId', '==', userId),
              where('startTime', '==', sessionToDelete.startTime)
            );
            const snap = await getDocs(q);
            const deletePromises = snap.docs.map(d => deleteDoc(d.ref));
            await Promise.all(deletePromises);
          }, 'deleteHistorySession');

          console.log('✅ Session supprimée sur Firebase et cache');
        } catch (error) {
          console.warn('⚠️ Échec suppression session Firebase, supprimée du cache local');
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Erreur suppression session:', error);
      return false;
    }
  }, [userId, connectionStatus.isFirebaseConnected]);

  // 🔧 Méthodes d'archivage simplifiées (pour compatibilité)
  const deleteArchivedSessions = useCallback(async (sessionsToDelete: Session[]): Promise<boolean> => {
    console.warn('deleteArchivedSessions: Implémentation simplifiée');
    return true;
  }, []);

  const deleteArchivedSession = useCallback(async (sessionToDelete: Session): Promise<boolean> => {
    console.warn('deleteArchivedSession: Implémentation simplifiée');
    return true;
  }, []);

  return {
    agencies,
    history,
    archives,
    saveSession,
    fetchHistory,
    fetchArchives,
    addAgency,
    deleteAgency,
    clearHistory,
    getOldSessions,
    exportToJSON,
    exportToCSV,
    exportToTXT,
    exportToPDF,
    archiveSessions,
    deleteHistorySession,
    deleteArchivedSessions,
    deleteArchivedSession,
    connectionStatus,
    isLoading,
  };
};
