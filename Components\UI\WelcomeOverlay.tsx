import React, { useState, useEffect } from 'react';
import { gsap } from 'gsap';

// 🎬 CISCO: Interface pour l'overlay de bienvenue avec flou
interface WelcomeOverlayProps {
  isVisible: boolean;
  onStartExperience: () => void;
  onStartAudio?: () => void; // 🔧 CISCO: Callback pour démarrer l'audio
}

// 🎬 CISCO: Overlay de bienvenue élégant avec flou d'arrière-plan
const WelcomeOverlay: React.FC<WelcomeOverlayProps> = ({ isVisible, onStartExperience, onStartAudio }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [isStarting, setIsStarting] = useState(false);

  // 🎬 CISCO: Animation d'apparition de l'overlay
  useEffect(() => {
    const overlay = document.querySelector('.welcome-overlay');
    if (overlay) {
      if (isVisible) {
        gsap.fromTo(overlay, 
          { 
            opacity: 0,
            backdropFilter: 'blur(0px)'
          },
          { 
            opacity: 1,
            backdropFilter: 'blur(20px)',
            duration: 1.5,
            ease: "power2.out"
          }
        );
      } else {
        gsap.to(overlay, {
          opacity: 0,
          backdropFilter: 'blur(0px)',
          duration: 1.2,
          ease: "power2.in"
        });
      }
    }
  }, [isVisible]);

  const handleClick = () => {
    if (isStarting || isPressed) return;

    setIsPressed(true);
    setIsStarting(true);

    // 🎬 CISCO: Animation de disparition avec effet
    const overlay = document.querySelector('.welcome-overlay');
    const content = document.querySelector('.welcome-content');
    
    if (overlay && content) {
      // Animation du bouton
      gsap.to(content.querySelector('.main-button'), {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });

      // Disparition de l'overlay après un délai
      setTimeout(() => {
        // 🔧 CISCO: Démarrer l'audio AVANT la disparition
        if (onStartAudio) {
          onStartAudio();
        }

        gsap.to(overlay, {
          opacity: 0,
          backdropFilter: 'blur(0px)',
          duration: 1.2,
          ease: "power2.in",
          onComplete: () => {
            onStartExperience();
          }
        });
      }, 300);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="welcome-overlay fixed inset-0 z-[10000] flex items-center justify-center"
         style={{
           backdropFilter: 'blur(20px)',
           backgroundColor: 'rgba(0, 0, 0, 0.3)'
         }}>
      
      {/* 🎬 CISCO: Contenu de la présentation */}
      <div className="welcome-content relative flex flex-col items-center space-y-8 p-8 max-w-2xl mx-auto text-center">
        
        {/* 🎬 CISCO: Titre principal magnifique */}
        <div className="space-y-4">
          <h1 className="text-5xl md:text-6xl font-bold text-white drop-shadow-2xl">
            ⏱️ TimeTracker V4
          </h1>
          <p className="text-2xl md:text-3xl text-gray-200 drop-shadow-lg font-light">
            Pointeuse professionnelle avec ambiance immersive
          </p>
          <p className="text-xl text-gray-300 drop-shadow-md">
            🌅 Lever de soleil • 🌤️ Nuages contemplatifs • 🎵 Ambiance sonore
          </p>
        </div>



        {/* 🎬 CISCO: Bouton principal magnifique */}
        <div className="relative">
          
          {/* 🎬 CISCO: Anneau de pulsation */}
          <div className="pulse-ring absolute inset-0 rounded-full border-4 border-white/30 pointer-events-none animate-pulse" />
          
          {/* 🎬 CISCO: Bouton principal */}
          <button
            className={`main-button relative group px-12 py-6 rounded-full font-bold text-xl transition-all duration-300 transform
              ${isPressed 
                ? 'bg-gradient-to-r from-orange-600 to-pink-600 scale-95' 
                : isHovered 
                  ? 'bg-gradient-to-r from-orange-500 to-pink-500 scale-105 shadow-2xl' 
                  : 'bg-gradient-to-r from-orange-400 to-pink-400 hover:scale-105'
              }
              text-white shadow-xl hover:shadow-2xl
              border-2 border-white/20 hover:border-white/40
              backdrop-blur-sm
            `}
            onClick={handleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            disabled={isPressed || isStarting}
          >
            {/* 🎬 CISCO: Effet de brillance */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent 
                          transform -skew-x-12 group-hover:animate-pulse" />
            
            {/* 🎬 CISCO: Texte du bouton */}
            <span className="relative z-10 flex items-center space-x-3">
              <span className="text-2xl">{isStarting ? '⏳' : '🚀'}</span>
              <span>{isStarting ? 'Démarrage...' : 'Découvrir l\'Expérience'}</span>
            </span>
          </button>
        </div>

        {/* 🎬 CISCO: Instructions subtiles */}
        <div className="text-center text-gray-400 text-sm space-y-1">
          <p>✨ Cliquez pour révéler l'ambiance immersive</p>
          <p>🔊 Audio disponible dans les contrôles</p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeOverlay;
