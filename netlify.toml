[build]
  # Commande de build pour Vite
  command = "npm run build"
  # Dossier de sortie après le build
  publish = "dist"

[build.environment]
  # Version de Node.js à utiliser
  NODE_VERSION = "18"

# Configuration pour les Single Page Applications (SPA)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de sécurité et Firebase
[[headers]]
  for = "/*"
  [headers.values]
    # Sécurité générale
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

    # Permissions Policy pour limiter les fonctionnalités (géolocalisation autorisée)
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self)"

    # Headers pour Firebase Authentication (résoudre les erreurs Cross-Origin-Opener-Policy)
    Cross-Origin-Opener-Policy = "same-origin-allow-popups"
    Cross-Origin-Embedder-Policy = "unsafe-none"

# Headers spécifiques pour les assets statiques
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers spécifiques pour les fichiers CSS
[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css"

# 🔧 CISCO: Headers spécifiques pour les fichiers audio
[[headers]]
  for = "/sounds/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    Content-Type = "audio/mpeg"

[[headers]]
  for = "*.mp3"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    Content-Type = "audio/mpeg"

# Configuration pour les formulaires Netlify (si nécessaire plus tard)
[forms]
  settings = { error_template = "form-errors.html" }
