import React, { useState, useRef } from 'react';
// import CinemaController from './CinemaController';
import ModeLeverSoleil, { ModeLeverSoleilRef } from '../Background/ModeLeverSoleil';

// 🎬 CISCO: Interface pour l'expérience lever de soleil avec volets
interface SunriseExperienceProps {
  isActive: boolean;
  timerDuration?: number;
  children?: React.ReactNode;
}

// 🎬 CISCO: Composant principal de l'expérience lever de soleil
const SunriseExperience: React.FC<SunriseExperienceProps> = ({
  isActive,
  timerDuration = 260,
  children
}) => {
  const [_experienceStarted] = useState(false);
  const modeLeverSoleilRef = useRef<ModeLeverSoleilRef>(null);

  // 🎬 CISCO: Fonctions d'expérience supprimées (non utilisées actuellement)

  // 🎬 CISCO: Reset de l'expérience
  // const resetExperience = () => {
  //   console.log('🎬 CISCO: Reset expérience lever de soleil');
  //   setExperienceStarted(false);
  // };

  return (
    <>
      {/* 🔧 CISCO: SUPPRESSION VOLETS - ModeLeverSoleil direct sans présentation */}
      <ModeLeverSoleil
        ref={modeLeverSoleilRef}
        isActive={isActive}
        timerDuration={timerDuration}
        startPaused={false}
        onAudioUnlock={() => {}}
      />

      {/* 🎬 CISCO: Contenu additionnel */}
      {children}
    </>
  );
};

export default SunriseExperience;
