import React, { useState, useEffect } from 'react';
import { gsap } from 'gsap';

// 🎬 CISCO: Interface pour le bouton de bienvenue élégant
interface WelcomeButtonProps {
  onStartExperience: () => void;
  isVisible: boolean;
}

// 🎬 CISCO: Magnifique bouton de bienvenue avec animations
const WelcomeButton: React.FC<WelcomeButtonProps> = ({ onStartExperience, isVisible }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [isStarting, setIsStarting] = useState(false); // 🔧 CISCO: Protection contre clics multiples

  // 🎬 CISCO: Animation d'apparition du bouton
  useEffect(() => {
    const button = document.querySelector('.welcome-button-container');
    if (button) {
      if (isVisible) {
        gsap.fromTo(button, 
          { 
            opacity: 0, 
            scale: 0.5, 
            y: 50 
          },
          { 
            opacity: 1, 
            scale: 1, 
            y: 0, 
            duration: 1.2, 
            ease: "back.out(1.7)",
            delay: 0.5
          }
        );
      } else {
        gsap.to(button, {
          opacity: 0,
          scale: 0.8,
          duration: 0.8,
          ease: "power2.in"
        });
      }
    }
  }, [isVisible]);

  // 🎬 CISCO: Animation de pulsation continue
  useEffect(() => {
    const pulseElement = document.querySelector('.pulse-ring');
    if (pulseElement && isVisible) {
      gsap.to(pulseElement, {
        scale: 1.5,
        opacity: 0,
        duration: 2,
        ease: "power2.out",
        repeat: -1,
        repeatDelay: 0.5
      });
    }
  }, [isVisible]);

  // 🔧 CISCO: NETTOYAGE - Plus de démarrage automatique ! Seulement manuel.

  const handleClick = () => {
    // 🔧 CISCO: Protection contre clics multiples
    if (isStarting || isPressed) {
      return;
    }

    setIsPressed(true);
    setIsStarting(true);

    // 🎬 CISCO: Animation de clic avec effet d'onde
    const button = document.querySelector('.main-button');
    if (button) {
      gsap.to(button, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut",
        onComplete: () => {
          // Délai pour l'effet visuel avant de démarrer
          setTimeout(() => {
            onStartExperience();
          }, 300);
        }
      });
    }
  };

  if (!isVisible) return null;

  return (
    <div className="welcome-button-container fixed inset-0 flex items-center justify-center z-[10000] pointer-events-auto">
      {/* 🎬 CISCO: Overlay semi-transparent pour focus */}
      <div className="absolute inset-0 bg-black/30 backdrop-blur-sm" />
      
      {/* 🎬 CISCO: Container principal du bouton */}
      <div className="relative flex flex-col items-center space-y-6">
        
        {/* 🎬 CISCO: Message de bienvenue élégant */}
        <div className="text-center space-y-3 mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-white drop-shadow-2xl">
            ⏱️ TimeTracker V4
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 drop-shadow-lg font-light">
            Pointeuse professionnelle avec ambiance immersive
          </p>
          <p className="text-lg text-gray-300 drop-shadow-md">
            🌅 Lever de soleil • 🎵 Ambiance sonore • Chronomètre pro
          </p>
        </div>

        {/* 🎬 CISCO: Bouton principal avec effets */}
        <div className="relative">
          
          {/* 🎬 CISCO: Anneau de pulsation */}
          <div className="pulse-ring absolute inset-0 rounded-full border-4 border-white/30 pointer-events-none" />
          
          {/* 🎬 CISCO: Bouton principal magnifique */}
          <button
            className={`main-button relative group px-12 py-6 rounded-full font-bold text-xl transition-all duration-300 transform
              ${isPressed 
                ? 'bg-gradient-to-r from-orange-600 to-pink-600 scale-95' 
                : isHovered 
                  ? 'bg-gradient-to-r from-orange-500 to-pink-500 scale-105 shadow-2xl' 
                  : 'bg-gradient-to-r from-orange-400 to-pink-400 hover:scale-105'
              }
              text-white shadow-xl hover:shadow-2xl
              border-2 border-white/20 hover:border-white/40
              backdrop-blur-sm
            `}
            onClick={handleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            disabled={isPressed || isStarting} // 🔧 CISCO: Désactiver si démarrage en cours
          >
            {/* 🎬 CISCO: Effet de brillance */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white/20 to-transparent 
                          transform -skew-x-12 group-hover:animate-pulse" />
            
            {/* 🎬 CISCO: Texte du bouton avec état */}
            <span className="relative z-10 flex items-center space-x-3">
              <span className="text-2xl">{isStarting ? '⏳' : '🚀'}</span>
              <span>{isStarting ? 'Démarrage...' : 'Démarrer l\'Application'}</span>
            </span>
          </button>

          {/* 🎬 CISCO: Particules flottantes autour du bouton */}
          <div className="absolute -inset-8 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white/40 rounded-full animate-pulse"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${10 + (i % 2) * 80}%`,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: `${2 + i * 0.2}s`
                }}
              />
            ))}
          </div>
        </div>

        {/* 🎬 CISCO: Instructions subtiles */}
        <div className="text-center text-gray-400 text-sm space-y-1 mt-6">
          <p>🔊 L'audio sera activé automatiquement</p>
          <p>⏱️ Durée de l'expérience : ~4 minutes</p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeButton;
