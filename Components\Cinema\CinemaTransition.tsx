import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { gsap } from 'gsap';

// 🎬 CISCO: Interface pour les volets cinématographiques
interface CinemaTransitionProps {
  children?: React.ReactNode;
}

// 🎬 CISCO: Interface pour les méthodes exposées
export interface CinemaTransitionRef {
  openCurtains: () => Promise<void>;
  closeCurtains: () => Promise<void>;
  resetCurtains: () => void;
}

// 🎬 CISCO: Composant volets cinématographiques avec ouverture ultra-progressive
const CinemaTransition = forwardRef<CinemaTransitionRef, CinemaTransitionProps>(
  ({ children }, ref) => {
    const leftCurtainRef = useRef<HTMLDivElement>(null);
    const rightCurtainRef = useRef<HTMLDivElement>(null);

    // 🔧 CISCO: DÉSACTIVATION ANIMATION VOLETS - Résolution immédiate
    const openCurtains = (): Promise<void> => {
      return new Promise((resolve) => {
        // console.log('🎬 CISCO: Volets désactivés - Résolution immédiate');
        resolve(); // Résolution immédiate sans animation
      });
    };

    // 🎬 CISCO: Fermeture des volets
    const closeCurtains = (): Promise<void> => {
      return new Promise((resolve) => {
        // console.log('🎬 CISCO: Fermeture volets');
        
        const timeline = gsap.timeline({
          onComplete: () => {
            // console.log('🎬 CISCO: Volets fermés');
            resolve();
          }
        });

        // Retour des volets à leur position initiale
        timeline
          .to(leftCurtainRef.current, {
            x: '0%',
            duration: 3.5,
            ease: 'power2.inOut'
          }, 0)
          .to(rightCurtainRef.current, {
            x: '0%',
            duration: 3.5,
            ease: 'power2.inOut'
          }, 0);
      });
    };

    // 🎬 CISCO: Reset des volets à leur position initiale
    const resetCurtains = () => {
      gsap.set([leftCurtainRef.current, rightCurtainRef.current], {
        x: '0%'
      });
    };

    // 🎬 CISCO: Exposer les méthodes via ref
    useImperativeHandle(ref, () => ({
      openCurtains,
      closeCurtains,
      resetCurtains
    }));

    return (
      <>
        {/* 🎬 CISCO: Volet gauche - DÉSACTIVÉ TEMPORAIREMENT POUR TEST NUAGES */}
        <div
          ref={leftCurtainRef}
          className="fixed inset-0 bg-black z-[9999] pointer-events-none"
          style={{
            width: '50.1%',
            left: '0%',
            borderRight: '1px solid #333',
            boxShadow: '2px 0 10px rgba(0,0,0,0.5)',
            display: 'none' // 🔧 CISCO: MASQUÉ POUR TEST
          }}
        />

        {/* 🎬 CISCO: Volet droit - DÉSACTIVÉ TEMPORAIREMENT POUR TEST NUAGES */}
        <div
          ref={rightCurtainRef}
          className="fixed inset-0 bg-black z-[9999] pointer-events-none"
          style={{
            width: '50.1%',
            left: '49.9%',
            borderLeft: '1px solid #333',
            boxShadow: '-2px 0 10px rgba(0,0,0,0.5)',
            display: 'none' // 🔧 CISCO: MASQUÉ POUR TEST
          }}
        />

        {/* 🎬 CISCO: Contenu affiché derrière les volets */}
        <div className="relative z-10">
          {children}
        </div>
      </>
    );
  }
);

CinemaTransition.displayName = 'CinemaTransition';

export default CinemaTransition;
