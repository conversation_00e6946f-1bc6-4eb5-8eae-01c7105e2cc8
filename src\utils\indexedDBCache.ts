// 🔧 CISCO: Système de cache IndexedDB pour persistance locale
// Résout le problème de session/historique sur Netlify

import { Session, Agency } from '../types';

interface CacheData {
  sessions: Session[];
  archives: Session[];
  agencies: Agency[];
  lastSync: number;
  userId: string;
}

class IndexedDBCache {
  private dbName = 'TimeTrackerCache';
  private version = 1;
  private db: IDBDatabase | null = null;

  // 🔧 Initialiser la base de données IndexedDB
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('❌ Erreur ouverture IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB initialisé avec succès');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Store pour les données utilisateur
        if (!db.objectStoreNames.contains('userData')) {
          const store = db.createObjectStore('userData', { keyPath: 'userId' });
          store.createIndex('lastSync', 'lastSync', { unique: false });
        }
      };
    });
  }

  // 🔧 Sauvegarder les données dans le cache local
  async saveToCache(userId: string, data: Partial<CacheData>): Promise<boolean> {
    if (!this.db) {
      console.warn('⚠️ IndexedDB non initialisé');
      return false;
    }

    try {
      const transaction = this.db.transaction(['userData'], 'readwrite');
      const store = transaction.objectStore('userData');
      
      // Récupérer les données existantes
      const existingData = await this.getFromCache(userId);
      
      // Fusionner avec les nouvelles données
      const updatedData: CacheData = {
        sessions: data.sessions || existingData?.sessions || [],
        archives: data.archives || existingData?.archives || [],
        agencies: data.agencies || existingData?.agencies || [],
        lastSync: Date.now(),
        userId
      };

      await new Promise<void>((resolve, reject) => {
        const request = store.put(updatedData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log('✅ Données sauvegardées dans le cache local');
      return true;
    } catch (error) {
      console.error('❌ Erreur sauvegarde cache:', error);
      return false;
    }
  }

  // 🔧 Récupérer les données du cache local
  async getFromCache(userId: string): Promise<CacheData | null> {
    if (!this.db) {
      console.warn('⚠️ IndexedDB non initialisé');
      return null;
    }

    try {
      const transaction = this.db.transaction(['userData'], 'readonly');
      const store = transaction.objectStore('userData');
      
      return new Promise<CacheData | null>((resolve, reject) => {
        const request = store.get(userId);
        
        request.onsuccess = () => {
          const result = request.result;
          if (result) {
            console.log('✅ Données récupérées du cache local');
            resolve(result);
          } else {
            resolve(null);
          }
        };
        
        request.onerror = () => {
          console.error('❌ Erreur lecture cache:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      console.error('❌ Erreur récupération cache:', error);
      return null;
    }
  }

  // 🔧 Vérifier si les données sont récentes (moins de 5 minutes)
  async isCacheRecent(userId: string): Promise<boolean> {
    const data = await this.getFromCache(userId);
    if (!data) return false;
    
    const fiveMinutes = 5 * 60 * 1000;
    return (Date.now() - data.lastSync) < fiveMinutes;
  }

  // 🔧 Nettoyer le cache (optionnel)
  async clearCache(userId: string): Promise<boolean> {
    if (!this.db) return false;

    try {
      const transaction = this.db.transaction(['userData'], 'readwrite');
      const store = transaction.objectStore('userData');
      
      await new Promise<void>((resolve, reject) => {
        const request = store.delete(userId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log('✅ Cache nettoyé pour l\'utilisateur');
      return true;
    } catch (error) {
      console.error('❌ Erreur nettoyage cache:', error);
      return false;
    }
  }

  // 🔧 Ajouter une session au cache
  async addSessionToCache(userId: string, session: Session): Promise<boolean> {
    const existingData = await this.getFromCache(userId);
    if (!existingData) return false;

    const updatedSessions = [session, ...existingData.sessions];
    return this.saveToCache(userId, { sessions: updatedSessions });
  }

  // 🔧 Supprimer une session du cache
  async removeSessionFromCache(userId: string, sessionId: string): Promise<boolean> {
    const existingData = await this.getFromCache(userId);
    if (!existingData) return false;

    const updatedSessions = existingData.sessions.filter(s => s.id !== sessionId);
    return this.saveToCache(userId, { sessions: updatedSessions });
  }

  // 🔧 Archiver des sessions dans le cache
  async archiveSessionsInCache(userId: string, sessionsToArchive: Session[]): Promise<boolean> {
    const existingData = await this.getFromCache(userId);
    if (!existingData) return false;

    const archivedIds = sessionsToArchive.map(s => s.id);
    const updatedSessions = existingData.sessions.filter(s => !archivedIds.includes(s.id));
    const updatedArchives = [...existingData.archives, ...sessionsToArchive];

    return this.saveToCache(userId, { 
      sessions: updatedSessions, 
      archives: updatedArchives 
    });
  }
}

// Instance singleton
export const indexedDBCache = new IndexedDBCache();

// 🔧 Initialiser le cache au démarrage de l'application
export const initializeCache = async (): Promise<boolean> => {
  try {
    await indexedDBCache.init();
    return true;
  } catch (error) {
    console.error('❌ Impossible d\'initialiser le cache IndexedDB:', error);
    return false;
  }
};
