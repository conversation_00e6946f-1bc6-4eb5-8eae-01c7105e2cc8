// 🔧 CISCO: Script de vérification des variables d'environnement
// Vérifie que toutes les variables Firebase sont correctement configurées

import { readFileSync } from 'fs';
import { join } from 'path';

const requiredVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

const optionalVars = [
  'VITE_FIREBASE_MEASUREMENT_ID',
  'VITE_FIRESTORE_DB_ID'
];

// Lire le fichier .env.local
let envVars = {};
try {
  const envContent = readFileSync('.env.local', 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      envVars[key.trim()] = valueParts.join('=').replace(/"/g, '').trim();
    }
  });
} catch (error) {
  console.log('⚠️  Fichier .env.local non trouvé');
}

console.log('🔍 Vérification des variables d\'environnement Firebase...\n');

// Vérifier les variables requises
let allRequiredPresent = true;
console.log('📋 Variables REQUISES:');
requiredVars.forEach(varName => {
  const value = envVars[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
  } else {
    console.log(`❌ ${varName}: MANQUANTE`);
    allRequiredPresent = false;
  }
});

console.log('\n📋 Variables OPTIONNELLES:');
optionalVars.forEach(varName => {
  const value = envVars[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value}`);
  } else {
    console.log(`⚠️  ${varName}: Non définie (optionnelle)`);
  }
});

console.log('\n🌐 Informations d\'environnement:');
console.log(`📍 NODE_ENV: ${process.env.NODE_ENV || 'non défini'}`);
console.log(`📍 Domaine actuel: ${process.env.URL || 'localhost'}`);

if (allRequiredPresent) {
  console.log('\n✅ Toutes les variables requises sont présentes !');
  process.exit(0);
} else {
  console.log('\n❌ Des variables requises sont manquantes !');
  console.log('\n💡 Solutions:');
  console.log('1. Vérifiez votre fichier .env.local');
  console.log('2. Sur Netlify: Site Settings → Environment Variables');
  console.log('3. Assurez-vous que les variables commencent par VITE_');
  process.exit(1);
}
