// 🔧 CISCO: Gestionnaire d'erreurs Firebase avancé
// Résout les erreurs 400 Bad Request et améliore la robustesse

export interface ConnectionStatus {
  isOnline: boolean;
  isFirebaseConnected: boolean;
  lastError: string | null;
  retryCount: number;
}

export class FirebaseErrorHandler {
  private connectionStatus: ConnectionStatus = {
    isOnline: navigator.onLine,
    isFirebaseConnected: true,
    lastError: null,
    retryCount: 0
  };

  private listeners: ((status: ConnectionStatus) => void)[] = [];
  private maxRetries = 5;
  private baseDelay = 1000; // 1 seconde

  constructor() {
    // Écouter les changements de connexion réseau
    window.addEventListener('online', () => {
      this.updateConnectionStatus({ isOnline: true });
    });

    window.addEventListener('offline', () => {
      this.updateConnectionStatus({ isOnline: false, isFirebaseConnected: false });
    });
  }

  // 🔧 Ajouter un listener pour les changements de statut
  addStatusListener(callback: (status: ConnectionStatus) => void): void {
    this.listeners.push(callback);
  }

  // 🔧 Supprimer un listener
  removeStatusListener(callback: (status: ConnectionStatus) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // 🔧 Mettre à jour le statut de connexion
  private updateConnectionStatus(updates: Partial<ConnectionStatus>): void {
    this.connectionStatus = { ...this.connectionStatus, ...updates };
    this.listeners.forEach(listener => listener(this.connectionStatus));
  }

  // 🔧 Obtenir le statut actuel
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  // 🔧 Retry avec backoff exponentiel
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    context: string = 'Firebase operation'
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Réinitialiser le compteur de retry en cas de succès
        if (attempt === 0) {
          this.updateConnectionStatus({ retryCount: 0, lastError: null });
        }

        const result = await operation();
        
        // Succès - marquer Firebase comme connecté
        if (!this.connectionStatus.isFirebaseConnected) {
          console.log('✅ Connexion Firebase rétablie');
          this.updateConnectionStatus({ 
            isFirebaseConnected: true, 
            lastError: null,
            retryCount: 0 
          });
        }

        return result;
      } catch (error: any) {
        lastError = error;
        const errorMessage = this.getErrorMessage(error);
        
        console.warn(`⚠️ Tentative ${attempt + 1}/${this.maxRetries} échouée pour ${context}:`, errorMessage);
        
        this.updateConnectionStatus({ 
          retryCount: attempt + 1,
          lastError: errorMessage 
        });

        // Ne pas réessayer pour certaines erreurs
        if (this.shouldNotRetry(error)) {
          console.error(`❌ Erreur non récupérable pour ${context}:`, errorMessage);
          this.updateConnectionStatus({ isFirebaseConnected: false });
          throw error;
        }

        // Attendre avant la prochaine tentative (backoff exponentiel)
        if (attempt < this.maxRetries - 1) {
          const delay = this.baseDelay * Math.pow(2, attempt);
          console.log(`⏳ Attente ${delay}ms avant nouvelle tentative...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Toutes les tentatives ont échoué
    console.error(`❌ Toutes les tentatives échouées pour ${context}:`, this.getErrorMessage(lastError));
    this.updateConnectionStatus({ 
      isFirebaseConnected: false,
      lastError: this.getErrorMessage(lastError)
    });
    
    throw lastError;
  }

  // 🔧 Déterminer si une erreur ne doit pas être retentée
  private shouldNotRetry(error: any): boolean {
    const errorCode = error?.code || '';
    const errorMessage = error?.message || '';

    // Erreurs d'authentification - ne pas réessayer
    if (errorCode.includes('auth/') || errorCode === 'permission-denied') {
      return true;
    }

    // Erreurs 400 Bad Request spécifiques
    if (errorCode === 'invalid-argument' || errorMessage.includes('400')) {
      return true;
    }

    // Erreurs de quota dépassé
    if (errorCode === 'resource-exhausted') {
      return true;
    }

    return false;
  }

  // 🔧 Extraire un message d'erreur lisible
  private getErrorMessage(error: any): string {
    if (!error) return 'Erreur inconnue';

    // Erreurs Firebase spécifiques
    if (error.code) {
      switch (error.code) {
        case 'unavailable':
          return 'Service Firebase temporairement indisponible';
        case 'permission-denied':
          return 'Permissions insuffisantes';
        case 'invalid-argument':
          return 'Paramètres de requête invalides';
        case 'resource-exhausted':
          return 'Quota Firebase dépassé';
        case 'deadline-exceeded':
          return 'Timeout de la requête Firebase';
        default:
          return `Erreur Firebase: ${error.code} - ${error.message}`;
      }
    }

    // Erreurs réseau
    if (error.message?.includes('Failed to fetch') || error.message?.includes('NetworkError')) {
      return 'Erreur de connexion réseau';
    }

    // Erreurs 400 Bad Request
    if (error.message?.includes('400') || error.status === 400) {
      return 'Requête malformée (400 Bad Request)';
    }

    return error.message || 'Erreur inconnue';
  }

  // 🔧 Valider une requête Firebase avant envoi
  validateFirebaseRequest(data: any, requiredFields: string[]): boolean {
    if (!data || typeof data !== 'object') {
      console.error('❌ Données de requête invalides:', data);
      return false;
    }

    for (const field of requiredFields) {
      if (!(field in data) || data[field] === undefined || data[field] === null) {
        console.error(`❌ Champ requis manquant: ${field}`);
        return false;
      }
    }

    return true;
  }

  // 🔧 Logger les détails d'une requête pour debug
  logRequestDetails(operation: string, data?: any): void {
    console.log(`🔍 Firebase ${operation}:`, {
      timestamp: new Date().toISOString(),
      online: navigator.onLine,
      firebaseConnected: this.connectionStatus.isFirebaseConnected,
      data: data ? JSON.stringify(data, null, 2) : 'N/A'
    });
  }
}

// Instance singleton
export const firebaseErrorHandler = new FirebaseErrorHandler();
