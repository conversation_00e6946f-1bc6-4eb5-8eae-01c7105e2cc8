#!/usr/bin/env node

/**
 * Script de vérification pré-déploiement
 * Vérifie que l'application est prête pour le déploiement Netlify
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN', 
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

console.log('🔍 Vérification pré-déploiement...\n');

// 1. Vérifier les fichiers critiques
const criticalFiles = [
  'package.json',
  'vite.config.ts',
  'netlify.toml',
  'firebase-config.ts',
  '.gitignore'
];

console.log('📁 Vérification des fichiers critiques...');
let allFilesExist = true;

criticalFiles.forEach(file => {
  if (existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
    allFilesExist = false;
  }
});

// 2. Vérifier .gitignore
console.log('\n🔒 Vérification .gitignore...');
const gitignoreContent = readFileSync('.gitignore', 'utf8');
const sensitivePatterns = ['.env', 'ContextEngineering/', '.logs', '.Backup/'];

sensitivePatterns.forEach(pattern => {
  if (gitignoreContent.includes(pattern)) {
    console.log(`✅ ${pattern} protégé`);
  } else {
    console.log(`⚠️  ${pattern} non protégé`);
  }
});

// 3. Vérifier la structure des variables d'environnement
console.log('\n🔧 Vérification structure variables d\'environnement...');
if (existsSync('.env.example')) {
  console.log('✅ .env.example présent');
} else {
  console.log('⚠️  .env.example manquant');
}

// 4. Vérifier netlify.toml
console.log('\n🌐 Vérification configuration Netlify...');
const netlifyConfig = readFileSync('netlify.toml', 'utf8');
if (netlifyConfig.includes('npm run build')) {
  console.log('✅ Commande de build configurée');
} else {
  console.log('❌ Commande de build manquante');
}

if (netlifyConfig.includes('publish = "dist"')) {
  console.log('✅ Dossier de publication configuré');
} else {
  console.log('❌ Dossier de publication manquant');
}

// 5. Résumé
console.log('\n📋 RÉSUMÉ');
if (allFilesExist) {
  console.log('✅ Tous les fichiers critiques sont présents');
  console.log('🚀 Application prête pour le déploiement Netlify');
  console.log('\n📝 N\'oubliez pas de configurer les variables d\'environnement dans Netlify !');
} else {
  console.log('❌ Des fichiers critiques sont manquants');
  console.log('🛑 Corrigez les problèmes avant le déploiement');
  process.exit(1);
}
