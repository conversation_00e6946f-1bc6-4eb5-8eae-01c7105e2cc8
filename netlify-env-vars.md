# 🔧 CISCO - Variables d'environnement pour Netlify

## Variables à configurer dans Netlify Dashboard

Aller dans : **Site Settings → Environment Variables**

### Variables REQUISES :

```
VITE_FIREBASE_API_KEY = AIzaSyBM_8bJBpCOzT3PnjzjQvlhDb5vS6AUEJ8
VITE_FIREBASE_AUTH_DOMAIN = florasynth-a461d.firebaseapp.com
VITE_FIREBASE_PROJECT_ID = florasynth-a461d
VITE_FIREBASE_STORAGE_BUCKET = florasynth-a461d.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID = 1040083472841
VITE_FIREBASE_APP_ID = 1:1040083472841:web:2d5429cfefbb9eac7c1818
```

### Variables OPTIONNELLES :

```
VITE_FIREBASE_MEASUREMENT_ID = G-3G3YNB8GKY
# VITE_FIRESTORE_DB_ID = (ne pas définir pour utiliser la DB par défaut)
```

## ⚠️ IMPORTANT pour résoudre l'erreur 400

**NE PAS DÉFINIR** `VITE_FIRESTORE_DB_ID` sur Netlify pour utiliser la base de données Firestore par défaut.

Cela évitera l'erreur :
```
firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?...&TYPE=terminate
Failed to load resource: the server responded with a status of 400 ()
```

## Vérification

Après configuration, l'application devrait afficher dans la console :
```
🔧 Firebase Configuration Debug: {
  projectId: "florasynth-a461d",
  authDomain: "florasynth-a461d.firebaseapp.com", 
  firestoreDbId: "(default)",
  currentDomain: "pointeuse-activite-pro.netlify.app",
  environment: "production"
}
```
