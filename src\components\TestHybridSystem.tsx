// 🔧 CISCO: Composant de test pour le système hybride
// Permet de tester le cache local et la gestion d'erreur Firebase

import React, { useState, useEffect } from 'react';
import { indexedDBCache, initializeCache } from '../utils/indexedDBCache';
import { firebaseErrorHandler } from '../utils/firebaseErrorHandler';
import { ConnectionStatus, ConnectionAlert } from './ConnectionStatus';

export const TestHybridSystem: React.FC = () => {
  const [cacheStatus, setCacheStatus] = useState<string>('Non initialisé');
  const [connectionStatus, setConnectionStatus] = useState(firebaseErrorHandler.getConnectionStatus());
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    // Écouter les changements de statut de connexion
    const handleStatusChange = (status: any) => {
      setConnectionStatus(status);
    };

    firebaseErrorHandler.addStatusListener(handleStatusChange);
    return () => firebaseErrorHandler.removeStatusListener(handleStatusChange);
  }, []);

  const testCache = async () => {
    try {
      const success = await initializeCache();
      if (success) {
        setCacheStatus('✅ Initialisé avec succès');
        
        // Test de sauvegarde/récupération
        const testData = {
          sessions: [],
          archives: [],
          agencies: [{ id: 'test-1', name: 'Test Agency' }],
          lastSync: Date.now(),
          userId: 'test-user'
        };

        await indexedDBCache.saveToCache('test-user', testData);
        const retrieved = await indexedDBCache.getFromCache('test-user');
        
        if (retrieved && retrieved.agencies.length > 0) {
          setTestResults(prev => [...prev, '✅ Cache: Sauvegarde/récupération OK']);
        } else {
          setTestResults(prev => [...prev, '❌ Cache: Échec sauvegarde/récupération']);
        }
      } else {
        setCacheStatus('❌ Échec initialisation');
      }
    } catch (error) {
      setCacheStatus(`❌ Erreur: ${error}`);
    }
  };

  const testFirebaseError = async () => {
    try {
      // Simuler une opération Firebase qui échoue
      await firebaseErrorHandler.retryWithBackoff(async () => {
        throw new Error('Test error 400 Bad Request');
      }, 'Test operation');
    } catch (error) {
      setTestResults(prev => [...prev, `✅ Gestion d'erreur: ${error}`]);
    }
  };

  const clearTests = () => {
    setTestResults([]);
  };

  return (
    <div className="fixed top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="text-lg font-bold mb-3">🔧 Test Système Hybride</h3>
      
      {/* Statut de connexion */}
      <div className="mb-3">
        <ConnectionStatus status={connectionStatus} className="mb-2" />
        <ConnectionAlert status={connectionStatus} />
      </div>

      {/* Statut du cache */}
      <div className="mb-3">
        <p className="text-sm"><strong>Cache IndexedDB:</strong> {cacheStatus}</p>
      </div>

      {/* Boutons de test */}
      <div className="flex gap-2 mb-3">
        <button
          onClick={testCache}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Test Cache
        </button>
        <button
          onClick={testFirebaseError}
          className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
        >
          Test Erreur
        </button>
        <button
          onClick={clearTests}
          className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          Clear
        </button>
      </div>

      {/* Résultats des tests */}
      {testResults.length > 0 && (
        <div className="bg-gray-100 rounded p-2 max-h-32 overflow-y-auto">
          <p className="text-xs font-semibold mb-1">Résultats:</p>
          {testResults.map((result, index) => (
            <p key={index} className="text-xs mb-1">{result}</p>
          ))}
        </div>
      )}
    </div>
  );
};
