{"name": "pointeuse-d-activite-pro", "private": true, "version": "1.0.0", "type": "module", "description": "Application de pointeuse d'activité pour freelances et intérimaires", "keywords": ["timetracker", "freelance", "firebase", "react", "typescript"], "scripts": {"dev": "npm run clean && vite", "build": "npm run clean && vite build", "preview": "vite preview", "clean": "npm cache clean --force", "type-check": "tsc --noEmit", "build:prod": "npm run type-check && vite build", "check-audio": "node scripts/check-audio-files.js", "build:check": "npm run build && npm run check-audio", "pre-deploy": "node scripts/pre-deploy-check.js", "deploy:ready": "npm run pre-deploy && npm run type-check && npm run build", "test": "vitest run", "test:watch": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"firebase": "^12.0.0", "gsap": "^3.13.0", "react": "^19.1.1", "react-dom": "^19.1.1", "suncalc": "^1.9.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/suncalc": "^1.9.2", "@vitejs/plugin-react": "^4.7.0", "jsdom": "^26.1.0", "terser": "^5.43.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.2.4"}}