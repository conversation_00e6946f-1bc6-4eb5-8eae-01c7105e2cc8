# 🕐 Pointeuse d'Activité Pro

Application web moderne de suivi du temps pour freelances et intérimaires.

## ✨ Fonctionnalités

- 🔐 **Authentification Google** sécurisée
- ⏱️ **Timer intelligent** avec détection d'inactivité
- 🏢 **Gestion multi-agences**
- 📝 **Journalisation détaillée** des activités
- 📊 **Historique et rapports** exportables
- 🌍 **Interface multilingue** (FR/EN)
- 🎯 **Vérifications aléatoires** d'activité

## 🚀 Démarrage rapide

### Prérequis
- Node.js 18+
- Compte Firebase avec projet configuré

### Installation

1. **<PERSON><PERSON><PERSON> le projet**
```bash
git clone [votre-repo]
cd pointeuse-d-activite-pro
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration Firebase**
```bash
cp .env.example .env.local
# Éditer .env.local avec vos clés Firebase
```

4. **Lancer en développement**

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the app:
   `npm run dev`
